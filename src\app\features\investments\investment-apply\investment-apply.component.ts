import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AbstractControl, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { DocumentType, TypeKey } from '@core/models/config';
import { DocumentService } from '@core/services/document.service';
import { DocumentPayload, InvestmentService } from '@core/services/investment.service';
import { Financial, Investment, InvestorsService } from '@core/services/investors.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbAlertModule,
  NbButtonModule,
  NbCheckboxModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSpinnerModule,
  NbToastrService,
  NbTooltipModule,
} from '@nebular/theme';
import { NgxMaskDirective } from 'ngx-mask';
@Component({
  selector: 'app-investment-apply',
  templateUrl: './investment-apply.component.html',
  styleUrls: ['./investment-apply.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NbIconModule,
    NbCheckboxModule,
    NbFormFieldModule,
    NbAlertModule,
    NbSpinnerModule,
    NbInputModule,
    NbTooltipModule,
    NbButtonModule,
    NgxMaskDirective,
  ],
})
export class InvestmentApplyComponent implements OnInit {
  @Output() changeTab = new EventEmitter<boolean>();

  @Input() getFormParamValue: any;

  financialsForm!: UntypedFormGroup;
  financialsData: any;
  loading = false;
  submitted = false;
  applied = false;
  returnUrl = '';
  error = '';
  isDocumentReaded = false;

  pattern = {
    X: {
      pattern: new RegExp('[#+(0-9)]'),
    },
    0: {
      pattern: new RegExp('[(0-9)]'),
    },
  };
  investorId: any;
  investorConsent = false;
  entityName: string | undefined;

  documents: any;
  investmentId: number;
  investmentTitle: string;
  entityTypeData: any;
  investmentStatusId: any;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private router: Router,
    public toastr: NbToastrService,
    protected cd: ChangeDetectorRef,
    private sharedService: SharedService,
    private investorsService: InvestorsService,
    private documentService: DocumentService,
    private investmentService: InvestmentService,
  ) {
    this.getFormParamValue = {};

    this.financialsForm = this.formBuilder.group({
      id: 0,
      bankAccountName: ['', Validators.required],
      accountNo: ['', Validators.required],
      bank: ['', [Validators.required]],
      bsb: ['', [Validators.required]],
      taxFileNo: ['', [Validators.required]],
      taxFileNoExcemptionCode: ['', [Validators.required]],
      investmentAmount: ['', [Validators.required]],
      investmentUnit: ['', [Validators.required]],
      investorConsent: ['', Validators.required],
    });

    this.investmentId = this.sharedService.getFormParamValue.investmentId || 0;
    this.investmentTitle = this.sharedService.getFormParamValue.investmentTitle || '';

    this.investmentService
      .getDocuments({
        documentType: DocumentType.Document,
        investmentId: this.investmentId,
        isInvestment: true,
      } as DocumentPayload)
      .subscribe((response: any) => {
        if (response.success) {
          this.documents = response.payload;
        }
      });
  }

  ngOnInit(): void {
    if (this.sharedService.isInvestor()) {
      this.investorId = this.investorsService.accountValue?.investorId;
      this.entityName = this.investorsService.accountValue?.name;
    } else {
      this.investorId = this.sharedService.getFormParamValue.investorId;
    }
    if (this.investorId) {
      this.investorsService.getFinancial(this.investorId).subscribe((data: any) => {
        if (data.success) {
          if (data.payload) {
            this.financialsData = data.payload;
            this.financialsForm.patchValue(data.payload);
          }
        } else {
          this.toastr.danger(data.error.message, 'Error!');
          this.loading = false;
        }
      });
    }

    this.getEntityType();

    this.financialsForm.controls.taxFileNoExcemptionCode.valueChanges.subscribe((updatedValue) => {
      this.updateControls();
    });
    this.financialsForm.controls.taxFileNo.valueChanges.subscribe((updatedValue) => {
      this.updateControls();
    });
  }

  validator(control: string): boolean {
    if (this.financialsForm && control) {
      const cont = this.financialsForm.get(control);
      if (!cont || !cont.validator) {
        return false;
      }
      const validator = cont.validator({} as AbstractControl);
      return validator && validator.required;
    } else {
      return false;
    }
  }

  private updateControls(): void {
    setTimeout(() => {
      if (this.financialsForm.value.taxFileNo && !this.financialsForm.value.taxFileNoExcemptionCode) {
        this.financialsForm.controls.taxFileNoExcemptionCode.clearValidators();
        this.financialsForm.controls.taxFileNoExcemptionCode.setErrors(null);
      } else if (this.financialsForm.value.taxFileNoExcemptionCode && !this.financialsForm.value.taxFileNo) {
        this.financialsForm.controls.taxFileNo.clearValidators();
        this.financialsForm.controls.taxFileNo.setErrors(null);
      } else if (!this.financialsForm.value.taxFileNoExcemptionCode && !this.financialsForm.value.taxFileNo) {
        this.financialsForm.controls.taxFileNoExcemptionCode.setValidators([Validators.required]);
        this.financialsForm.controls.taxFileNo.setValidators([Validators.required]);
        this.financialsForm.controls.taxFileNoExcemptionCode.setErrors({
          required: true,
        });
        this.financialsForm.controls.taxFileNo.setErrors({ required: true });
      }
    }, 500);
    this.cd.detectChanges();
  }

  onInputValue(event: any): void {
    this.financialsForm.patchValue({
      investmentUnit: this.financialsForm.value.investmentAmount,
    });
  }

  private getEntityType(): void {
    this.investmentService
      .getEntityType({
        typeKey: TypeKey.InvestmentStatus,
      })
      .subscribe((userData: any) => {
        if (userData.success) {
          this.entityTypeData = userData.payload;
          const [seletedStatus] = this.entityTypeData.filter((t: any) => t.name === 'Application');
          this.investmentStatusId = seletedStatus.id;
        }
      });
  }

  async downloadFile(file: any): Promise<void> {
    this.isDocumentReaded = true;
    this.toastr.default(`Downloading started`, 'Success!', {
      icon: 'download',
    });
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }

  toggle(checked: boolean): void {
    this.financialsForm.patchValue({
      investorConsent: checked ? true : null,
    });
  }

  get f() {
    return this.financialsForm.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    if (!this.financialsForm.value.taxFileNoExcemptionCode && !this.financialsForm.value.taxFileNo) {
      this.financialsForm.controls.taxFileNoExcemptionCode.setValidators([Validators.required]);
      this.financialsForm.controls.taxFileNo.setValidators([Validators.required]);
      return;
    }
    // stop here if form is invalid
    if (this.financialsForm.invalid) {
      return;
    }
    this.loading = true;

    this.updateFinancials();
  }

  private updateFinancials(): void {
    const financialsData: Financial = {
      investorId: this.investorId,
      id: this.financialsForm.value.id,
      bankAccountName: this.financialsForm.value.bankAccountName,
      accountNo: this.financialsForm.value.accountNo,
      bank: this.financialsForm.value.bank,
      host: window.location.host,
      bsb: this.financialsForm.value.bsb,
      taxFileNo: this.financialsForm.value.taxFileNo,
      taxFileNoExcemptionCode: this.financialsForm.value.taxFileNoExcemptionCode,
    };

    const investmentData: Investment[] = [
      {
        investorId: this.investorId,
        investmentId: this.investmentId,
        investmentUnit: this.financialsForm.value.investmentUnit,
        investmentAmount: this.financialsForm.value.investmentAmount,
        forApproval: true,
        investmentStatusId: this.investmentStatusId,
        investorConsent: this.financialsForm.value.investorConsent,
      },
    ];

    this.investorsService
      .saveInvestment(investmentData, financialsData, {
        entityName: this.entityName,
        investmentName: this.investmentTitle,
      })
      .subscribe(
        (data: any) => {
          setTimeout(() => {
            if (data.success) {
              this.loading = false;
              this.toastr.success('Saved Successfully', 'Success!');
              this.applied = true;
              if (!this.financialsData) {
                this.changeTab.emit(true);
              }
            } else {
              this.toastr.danger(data.error.message, 'Error!');
              this.loading = false;
            }
          }, 200);
        },
        (err: any) => {
          this.toastr.danger(err.error.message, 'Error!');
          this.loading = false;
        },
      );
  }

  backtoList(): void {
    this.router.navigate(['/users']);
  }
}
