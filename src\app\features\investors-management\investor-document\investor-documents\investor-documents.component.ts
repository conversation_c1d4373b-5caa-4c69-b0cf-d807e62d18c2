import { CommonModule } from '@angular/common';
import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { DeleteDocumentComponent } from '@components/templates/delete-document/delete-document.component';
import { DocumentService } from '@core/services/document.service';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogService,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { SkeletonModule } from 'primeng/skeleton';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { Subject } from 'rxjs';
import { AddDocumentComponent } from '../add-document/add-document.component';

@Component({
  selector: 'app-investor-documents',
  templateUrl: './investor-documents.component.html',
  styleUrls: ['./investor-documents.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbFormFieldModule,
    NbInputModule,
    TableModule,
    SkeletonModule,
    NbButtonModule,
  ],
})
export class InvestorDocumentsComponent implements OnInit, OnDestroy, AfterViewChecked {
  @Output() changeTab = new EventEmitter<boolean>();
  @Input() removeAction: any;
  @Input() allDocs = false;
  @ViewChild('dt') dt: any;
  @ViewChild('admin', { static: false }) admin: any;
  documents: any[] = [];
  statusData: any[] = [];
  totalRecords = 0;
  filterParams: Filters = {};
  eventFilters: any;
  loading = false;

  dtTrigger: Subject<any> = new Subject<any>();
  roleData: any;
  investorId: number | undefined;
  dateFilterData: any[] = [];
  constructor(
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private investorsService: InvestorsService,
    private dialogService: NbDialogService,
    private documentService: DocumentService,
    private cdr: ChangeDetectorRef,
  ) {}

  async ngOnInit(): Promise<void> {
    this.dateFilterData = this.sharedService.getDateFilterRows();

    if (this.isInvestor()) {
      this.investorId = this.investorsService.accountValue?.investorId;
    } else {
      if (this.sharedService.getFormParamValue) {
        this.investorId = this.sharedService.getFormParamValue.investorId;
      }
    }

    this.filterParams = {
      pageNumber: 1,
      pageSize: this.investorId ? 100 : 10,
    } as Filters;
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getList(): void {
    if (!this.allDocs) {
      if (this.investorId) {
        this.filterParams.investorId = this.investorId;
      }
    }
    this.investorsService.getDocuments(this.filterParams).subscribe((data: any) => {
      if (data.success) {
        this.documents = (data as any).payload.investorDocuments;
        this.totalRecords = (data as any).payload.rows;
        this.dtTrigger.next(this.documents);
        this.spinner.hide();
        this.loading = false;
      }
    });
  }

  isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  nextPage(event: TableLazyLoadEvent): void {
    this.loading = true;
    this.eventFilters = event.filters;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.filterParams.export = false;
    this.getList();
  }

  exportUser(): void {
    this.filterParams.export = true;
  }

  ngOnDestroy(): void {}

  addNewDocument(): void {
    this.dialogService
      .open(AddDocumentComponent, {
        context: {},
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.getList();
        }
      });
  }

  getStatusState(status: string): string {
    return this.sharedService.getStatusState(status);
  }

  timeAgo(time: any): any {
    return this.sharedService.timeAgo(time);
  }

  filterGlobal(event: any): void {
    this.dt.filterGlobal(event.target.value, 'contains');
  }

  deleteDocument(documentKey: string, userId: any, investorId: any): void {
    this.dialogService
      .open(DeleteDocumentComponent, {
        context: {
          documentKey,
          userId,
          investorId,
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.getList();
        }
      });
  }

  async downloadFile(file: any): Promise<void> {
    this.toast.default(`Downloading started`, 'Success!', {
      icon: 'download',
    });
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }

  next(): void {
    this.changeTab.emit(true);
  }
}
