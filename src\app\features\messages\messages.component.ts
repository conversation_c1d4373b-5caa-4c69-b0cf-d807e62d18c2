import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { InvestorChatComponent } from '@components/templates/investor-chat/investor-chat.component';
import { GlobalMessageType } from '@core/helpers';
import { GlobalChatRequest } from '@core/models/request/global-chat-request';
import { GlobalChat, GlobalChatResponse } from '@core/models/response/global-chat.response';
import { AssetService } from '@core/services/asset.service';
import { DashboardService } from '@core/services/dashboard.service';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbListModule,
  NbUserModule,
} from '@nebular/theme';
import { BadgeModule } from 'primeng/badge';
import { DrawerModule } from 'primeng/drawer';
import { AssetChatComponent } from '../lender/asset-chat/asset-chat.component';

@Component({
  selector: 'app-messages',
  templateUrl: './messages.component.html',
  styleUrls: ['./messages.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbIconModule,
    NbFormFieldModule,
    NbListModule,
    NbUserModule,
    NbButtonModule,
    NbInputModule,
    DrawerModule,
    InvestorChatComponent,
    AssetChatComponent,
    BadgeModule,
  ],
})
export class MessagesComponent implements OnInit, OnChanges {
  stateOptions!: any[];
  globalMessageType!: GlobalMessageType;

  @Input() visible = false;

  @Output() hidePopup = new EventEmitter<boolean>();

  @Output() updateNotificationCount = new EventEmitter<boolean>();

  @Input() investorId = 0;

  @Input() userId = 0;

  filterParams: Filters = {};

  assetFilterParams: GlobalChatRequest = {};

  investors: any[] = [];
  assetTypeUsers: GlobalChat[] = [];
  openChat = true;

  selectedInvestor: any;
  selectedAssetUser!: GlobalChat;

  constructor(
    private sharedService: SharedService,
    private dashboardService: DashboardService,
    private investorsService: InvestorsService,
    private assetService: AssetService,
  ) {
    if (this.sharedService.isInvestor() || this.sharedService.isInvestorStaffUsers()) {
      this.globalMessageType = GlobalMessageType.Investor;
    } else if (this.sharedService.isAdmin()) {
      this.globalMessageType = GlobalMessageType.Investor;
    } else if (this.sharedService.isOriginatorManager() || this.sharedService.isLender()) {
      this.globalMessageType = GlobalMessageType.Asset;
    }
  }

  ngOnInit(): void {
    this.stateOptions = [
      { label: 'Investor', value: GlobalMessageType.Investor },
      { label: 'Asset', value: GlobalMessageType.Asset },
    ];
    if (this.hasGlobalMessageTypeAsset()) {
      this.getAssetGlobalChatList();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.filterParams = {
      pageNumber: 1,
      pageSize: 10,
      sortOrder: 'asc',
      sortField: 'entityName',
      userTypeId: 1, // Client users only
    } as Filters;

    this.assetFilterParams = {
      pageNumber: 1,
      pageSize: 10,
      sortOrder: 'asc',
      sortField: 'contactName',
    } as GlobalChatRequest;

    if ('investorId' in changes && this.hasGlobalMessageTypeInvestor()) {
      this.investorId = changes.investorId.currentValue;
      this.getInvestorList();
    }
    if ('userId' in changes && this.hasGlobalMessageTypeAsset()) {
      this.userId = changes.userId.currentValue;
      this.getAssetGlobalChatList();
    }
  }

  hasGlobalMessageTypeAsset(): boolean {
    return this.globalMessageType === GlobalMessageType.Asset;
  }

  hasGlobalMessageTypeInvestor(): boolean {
    return this.globalMessageType === GlobalMessageType.Investor;
  }

  getAssetGlobalChatList(resetSelection = false): void {
    this.assetService.getGlobalChatList(this.assetFilterParams).subscribe((data: GlobalChatResponse) => {
      if (data.success) {
        this.assetTypeUsers = data.payload;
        if ((this.assetTypeUsers.length > 0 && !this.userId) || resetSelection) {
          this.userId = this.assetTypeUsers[0].userId;
          this.selectedAssetUser = this.assetTypeUsers[0];
        }
        this.scrollToInvestor(this.userId);
      }
    });
  }

  private getInvestorList(resetSelection = false): void {
    if (!this.isInvestor()) {
      this.dashboardService.getInvestorGlobalChat(this.filterParams).subscribe((data: any) => {
        if (data.success) {
          this.investors = (data as any).payload;
          if ((this.investors.length > 0 && !this.investorId) || resetSelection) {
            this.investorId = this.investors[0].investorId;
            this.selectedInvestor = this.investors[0];
            this.getInvestorMessages(this.investors[0]);
          }
          this.scrollToInvestor(this.investorId);
        }
      });
    } else {
      this.investors = [
        {
          contactName: 'Sydney Wyde Support',
          dateCreated: '',
          investorId: this.investorId,
          message: '',
        },
      ];
      this.getInvestorMessages(this.investors[0]);
    }
  }

  timeAgo(time: any): any {
    return this.sharedService.timeAgo(time);
  }

  isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  isInvestorStaffUsers(): boolean {
    return this.sharedService.isInvestorStaffUsers();
  }

  isAssetUsers(): boolean {
    return this.globalMessageType === GlobalMessageType.Asset;
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  getInvestorMessages(user: any): void {
    this.investorId = 0;
    this.openChat = true;
    this.selectedInvestor = user;

    setTimeout(() => {
      this.investorId = user.investorId;
      this.updateInvestorNotification();
      user.isRead = true;
    }, 20);
  }

  getAssetUserMessages(user: GlobalChat): void {
    this.userId = 0;
    this.openChat = true;
    this.selectedAssetUser = user;

    setTimeout(() => {
      this.userId = user.userId;
      this.updateAssetNotification(user);
      user.isRead = true;
    }, 20);
  }

  private updateInvestorNotification(): void {
    this.investorsService.getUpdateNotification({ investorId: this.investorId }).subscribe((response: any) => {
      this.updateNotificationCount.emit(true);
    });
  }

  private updateAssetNotification(user: any): void {
    this.assetService.getUpdateNotification(user.userId).subscribe((response: any) => {
      this.updateNotificationCount.emit(true);
    });
  }

  onHide(): void {
    this.hidePopup.emit(true);
  }

  filterInvestors(event: any): void {
    // event.target.value
    setTimeout(() => {
      this.filterParams.search = event.target.value;
      this.getInvestorList();
    }, 200);
  }

  sortInvestorList(value: any): void {
    this.filterParams.sortField = 'entityName';
    this.filterParams.sortOrder = value;
    this.getInvestorList(true);
  }

  sortAssetUsersList(value: any): void {
    this.assetFilterParams.sortField = 'contactName';
    this.assetFilterParams.sortOrder = value;
    this.getAssetGlobalChatList(true);
  }

  scrollToInvestor(investorId: any): void {
    setTimeout(() => {
      const elementList = document.querySelectorAll('.selected' + investorId);
      const element = elementList[0] as HTMLElement;
      element.scrollIntoView({ behavior: 'smooth' });
    }, 200);
  }

  onMessageTypeChange(event: any): void {
    setTimeout(() => {
      if (this.hasGlobalMessageTypeInvestor()) {
        this.getInvestorList();
      } else if (this.hasGlobalMessageTypeAsset()) {
        this.getAssetGlobalChatList();
      }
    }, 20);
  }
}
